# WebP 图片转换脚本使用说明

这个脚本可以将常见的图片格式（JPG、JPEG、PNG）转换为 WebP 格式，支持有损和无损两种压缩模式。

## 安装依赖

在使用脚本之前，请确保安装了必要的依赖：

```bash
npm install sharp commander
```

## 基本用法

```bash
node 转换WebP.js [选项]
```

## 命令行选项

| 选项 | 简写 | 描述 | 默认值 |
|------|------|------|--------|
| `--directory <path>` | `-d` | 要处理的目录路径 | `./src/assets/imgs/` |
| `--quality <number>` | `-q` | 有损模式的质量 (1-100) | `80` |
| `--lossless` | `-l` | 使用无损模式 | `false` |
| `--replace` | `-r` | 替换原始文件 | `false` |
| `--extensions <list>` | `-e` | 要处理的文件扩展名列表 | `jpg,jpeg,png` |

## 使用示例

### 1. 基本转换（有损模式，质量80）
```bash
node 转换WebP.js
```

### 2. 无损转换
```bash
node 转换WebP.js --lossless
```

### 3. 指定质量的有损转换
```bash
node 转换WebP.js --quality 90
```

### 4. 替换原始文件
```bash
node 转换WebP.js --replace
```

### 5. 指定目录
```bash
node 转换WebP.js --directory ./images/
```

### 6. 只处理特定格式
```bash
node 转换WebP.js --extensions png,jpg
```

### 7. 组合使用
```bash
node 转换WebP.js --directory ./src/assets/ --lossless --replace
```

## 模式说明

### 有损模式（默认）
- 文件体积更小
- 质量可调节（1-100）
- 适合照片和复杂图像
- 推荐质量设置：70-90

### 无损模式
- 保持原始图像质量
- 文件体积相对较大
- 适合图标、logo等需要保持清晰度的图像
- 对于简单图像压缩效果更好

## 输出行为

### 不替换原始文件（默认）
- 原始文件保持不变
- 在同一目录下生成 `.webp` 文件
- 例如：`image.jpg` -> `image.webp`

### 替换原始文件
- 删除原始文件
- 生成同名的 `.webp` 文件
- 例如：`image.jpg` -> `image.webp`（原 jpg 文件被删除）

## 注意事项

1. **备份重要文件**：使用 `--replace` 选项前请备份重要图片
2. **递归处理**：脚本会递归处理指定目录下的所有子目录
3. **文件格式**：目前支持 JPG、JPEG、PNG 格式
4. **性能**：大量图片转换可能需要较长时间
5. **兼容性**：WebP 格式在现代浏览器中支持良好

## 错误处理

脚本包含错误处理机制：
- 转换失败的文件会显示错误信息
- 不会中断整个转换过程
- 继续处理其他文件

## 性能建议

- 对于照片类图像，建议使用有损模式，质量设置为 75-85
- 对于图标、logo等，建议使用无损模式
- 大批量转换时建议分批处理
- 转换前可以先在小范围测试效果