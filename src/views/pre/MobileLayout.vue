<template>
  <div class="mobile-layout">
    <!-- 第1屏 - 首页 -->
    <section class="mobile-section section-1" :style="getSectionStyle('p1')">
      <P1 :is-active="visibleSections.includes(0)" :main-swiper-instance="null" />
    </section>

    <!-- 第2屏 - 预约 -->
    <section class="mobile-section section-2" :style="getSectionStyle('p2')">
      <P2 :is-active="visibleSections.includes(1)" />
    </section>

    <!-- 第3屏 - 抽奖 -->
    <section class="mobile-section section-3" :style="getSectionStyle('p3')">
      <P3 :is-active="visibleSections.includes(2)" :rules-pop-ref="rulesPopRef" />
    </section>

    <!-- 第4屏 - 里程碑 -->
    <section class="mobile-section section-4" :style="getSectionStyle('p4')">
      <P4 :is-active="visibleSections.includes(3)" :reservation-count="reservationCount"
        :current-level="currentLevel" />
    </section>

    <!-- 第5屏 - 职业介绍 -->
    <section class="mobile-section section-5" :style="getSectionStyle('p5')">
      <P5 :is-active="visibleSections.includes(4)" :initial-character="selectedCharacter" />
    </section>

    <!-- 第6屏 - 公会 -->
    <section class="mobile-section section-6" :style="getSectionStyle('p6')">
      <P6 :is-active="visibleSections.includes(5)" :guilds="guilds" :page-size="pageSize" />
    </section>

    <!-- 第7屏 - 游戏世界观介绍 -->
    <section class="mobile-section section-7" :style="getSectionStyle('p7-2')">
      <P7 :is-active="visibleSections.includes(6)" :initial-tab="activeTab" @tab-change="switchTab" />
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useResizeHandler } from '@/hooks/useResizeHandler'

// 导入页面组件
import P1 from '@/views/pre/P1.vue'
import P2 from '@/views/pre/P2.vue'
import P3 from '@/views/pre/P3.vue'
import P4 from '@/views/pre/P4.vue'
import P5 from '@/views/pre/P5.vue'
import P6 from '@/views/pre/P6.vue'
import P7 from '@/views/pre/P7.vue'

// Props 定义
interface Props {
  rulesPopRef?: any
  reservationCount?: number
  currentLevel?: number
  selectedCharacter?: number
  guilds?: Array<{
    id: number
    name: string
    link: string
    status: 'available' | 'full'
  }>
  pageSize?: number
  activeTab?: string
}

const props = withDefaults(defineProps<Props>(), {
})

// Emits 定义
const emit = defineEmits<{
  tabChange: [tab: string]
}>()

useResizeHandler()

// 响应式数据
const visibleSections = ref<number[]>([0]) // 默认第一屏可见

// 获取移动端背景图片样式
const getSectionStyle = (bgName: string) => {
  // 使用 new URL 来正确处理 Vite 中的静态资源
  const bgUrl = new URL(`../../assets/imgs/bg/m/${bgName}.jpg`, import.meta.url).href
  return {
    backgroundImage: `url(${bgUrl})`,
    backgroundSize: 'contain',
    backgroundPosition: 'top',
    backgroundRepeat: 'no-repeat'
  }
}

// Tab 切换功能
const switchTab = (tab: string) => {
  emit('tabChange', tab)
}

// Intersection Observer 用于检测可见区域
let observer: IntersectionObserver | null = null

const setupIntersectionObserver = () => {
  const options = {
    root: null,
    rootMargin: '-20% 0px -20% 0px', // 当元素进入视口中间60%区域时触发
    threshold: 0.3 // 当30%的元素可见时触发
  }

  observer = new IntersectionObserver((entries) => {
    const newVisibleSections: number[] = []

    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const sectionElement = entry.target as HTMLElement
        const sectionIndex = parseInt(sectionElement.dataset.sectionIndex || '0')
        newVisibleSections.push(sectionIndex)
      }
    })

    // 更新可见区域，保持至少一个区域可见
    if (newVisibleSections.length > 0) {
      visibleSections.value = newVisibleSections
    }
  }, options)

  // 观察所有section
  const sections = document.querySelectorAll('.mobile-section')
  sections.forEach((section, index) => {
    (section as HTMLElement).dataset.sectionIndex = index.toString()
    observer?.observe(section)
  })
}

// 生命周期
onMounted(() => {
  setupIntersectionObserver()
})

onUnmounted(() => {
  if (observer) {
    observer.disconnect()
  }
  window.removeEventListener('resize', () => { })
})
</script>

<style lang="less" scoped>
.mobile-layout {
  width: 100%;

  .mobile-section {
    width: 7.5rem;
    height: 13.34rem;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: hidden;
  }
}
</style>
