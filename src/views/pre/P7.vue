<template>
  <div class="slide-content world-view-container">
    <!-- Tab 切换导航 -->
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="tab-navigation">
        <button :class="['tab-btn', 'tab1', { active: activeTab === 'tab1' }]" @click="switchTab('tab1')">
        </button>
        <button :class="['tab-btn', 'tab2', { active: activeTab === 'tab2' }]" @click="switchTab('tab2')">
        </button>
      </div>
    </transition>

    <!-- Tab 内容区域 -->
    <div class="tab-content">
      <!-- Tab Swiper: 使用fade效果的全屏切换 -->
      <Swiper :modules="[EffectFade]" effect="fade" :fade-effect="{ crossFade: true }" :allow-touch-move="false"
        :navigation="false" :pagination="false" :scrollbar="false" class="tab-swiper" @swiper="onTabSwiperReady"
        @slide-change="onTabSwiperChange">
        <!-- Tab1: 水平全屏轮播 -->
        <swiper-slide class="tab-panel tab1-panel">
          <WorldViewSwiper />
        </swiper-slide>

        <!-- Tab2: 固定背景 + 居中轮播 -->
        <swiper-slide class="tab-panel tab2-panel">
          <StorySwiper />
        </swiper-slide>
      </Swiper>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectFade } from 'swiper/modules'
import WorldViewSwiper from '@/components/swiper/WorldViewSwiper.vue'
import StorySwiper from '@/components/swiper/StorySwiper.vue'

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean
  initialTab?: string
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  initialTab: 'tab1'
})

// Emits 定义
const emit = defineEmits<{
  tabChange: [tab: string]
}>()

// 响应式数据
const activeTab = ref(props.initialTab)
const tabSwiperInstance = ref<any>(null)

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab
  emit('tabChange', tab)

  // 控制tab swiper切换
  if (tabSwiperInstance.value) {
    const slideIndex = tab === 'tab1' ? 0 : 1
    tabSwiperInstance.value.slideTo(slideIndex)
  }
}

// Tab Swiper 事件处理
const onTabSwiperReady = (swiper: any) => {
  tabSwiperInstance.value = swiper
}

const onTabSwiperChange = (swiper: any) => {
  const currentIndex = swiper.activeIndex
  const newTab = currentIndex === 0 ? 'tab1' : 'tab2'
  activeTab.value = newTab
  emit('tabChange', newTab)
}

watch(() => props.initialTab, (tab) => {
  switchTab(tab)
})
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.world-view-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  position: relative;

  .tab-navigation {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    z-index: 100;
    position: absolute;
    top: 2rem;
    left: 50%;
    transform: translateX(-50%);

    .tab-btn {
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      background-repeat: no-repeat;
      background-position: center;
      width: 351px;
      height: 89px;

      .active {
        width: 345px;
        height: 184px;
      }

      &:hover {
        transform: scale(1.05);
        filter: brightness(1.1);
      }

      &.tab1 {
        background-image: url(@/assets/imgs/p7/tab1.png);

        &.active {
          background-image: url(@/assets/imgs/p7/tab1-active.png);
        }
      }

      &.tab2 {
        background-image: url(@/assets/imgs/p7/tab2.png);

        &.active {
          background-image: url(@/assets/imgs/p7/tab2-active.png);
        }
      }
    }
  }

  .tab-content {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  // Tab Swiper 样式
  .tab-swiper {
    width: 100%;
    height: 100%;

    .swiper-wrapper {
      width: 100%;
      height: 100%;
    }

    .swiper-slide {
      width: 100%;
      height: 100%;
      position: relative;
    }
  }

  .tab-panel {
    width: 100%;
    height: 100%;
    position: relative;

    // Tab1 - 水平全屏轮播样式
    &.tab1-panel {
      // 确保Tab1面板占据整个第7屏
      width: 100vw;
      height: 100vh;
      z-index: 10;
    }

    // Tab2 - 固定背景样式
    &.tab2-panel {
      background-image: url('@/assets/imgs/bg/p7-2.jpg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100vh;
      z-index: 10;
    }
  }
}

// 移动端响应式适配
@media (max-width: 540px) {
  .world-view-container {
    .tab-navigation {
      gap: 0;
      margin: 0 auto;
      top: .3rem;
      align-items: center;

      .tab-btn {
        width: 3.51rem;
        height: 0.89rem;
        background-size: contain;

        &.active {
          width: 3.45rem;
          height: 1.84rem;
        }
      }
    }

    .tab-panel {

      // Tab1 - 水平全屏轮播样式
      &.tab1-panel {
        width: 100vw;
        height: 100vh;
      }

      // Tab2 - 固定背景样式
      &.tab2-panel {
        background-image: url('@/assets/imgs/bg/m/p7-2.jpg');
        background-size: cover;
        width: 100%;
        height: 100vh;
      }
    }
  }
}
</style>
