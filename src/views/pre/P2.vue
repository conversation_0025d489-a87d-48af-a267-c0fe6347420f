<template>
  <div class="slide-content">
    <ParticleEffect />
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p2/title.png" alt="">
      </div>
    </transition>
    <div class="content">
      <!-- 预约奖励 -->
      <div class="rewards">
        <WebM class="reward" :src="WebMReservationReward1" />
        <WebM class="reward" :src="WebMReservationReward2" />
      </div>
      <!-- 平台选择按钮 -->
      <div class="platform-buttons">
        <button class="platform-btn aos-btn" :class="{ active: selectedPlatform === 'aos' }"
          @click="selectedPlatform = 'aos'">
        </button>
        <button class="platform-btn ios-btn" :class="{ active: selectedPlatform === 'ios' }"
          @click="selectedPlatform = 'ios'">
        </button>
      </div>
      <div class="phone-input">
        <PhoneInput v-model="phoneNumber" />
      </div>
      <div class="rule-btn">
        <img src="@/assets/imgs/p2/rule-btn.png" alt="">
      </div>
      <div class="reservation-btn">
        <WebM class="reservation breathe" :src="WebMP2ReservationBtn" />
      </div>
      <div class="big-reward">
        <WebM class="reward" :src="WebMReservationReward3" />
      </div>
      <div class="store-buttons">
        <WebM class="store-button breathe" :src="WebMGooglePlay" @click="handleStoreClick('google')" />
        <WebM class="store-button breathe" :src="WebMAppleStore" @click="handleStoreClick('apple')" />
      </div>
      <div class="community-btn">
        <WebM class="community breathe" :src="WebMP2CommunityBtn" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WebM from '@/components/WebM.vue'
import PhoneInput from '@/components/PhoneInput.vue'
import ParticleEffect from '@/components/ParticleEffect.vue'

// Hooks
import { useDeviceDetection } from '@/hooks/useDeviceDetection'

// WebM 资源导入
import WebMGooglePlay from '@/assets/imgs/webm/google-play.webm'
import WebMAppleStore from '@/assets/imgs/webm/apple-store.webm'
import WebMP2ReservationBtn from '@/assets/imgs/webm/p2-reservation-btn.webm'
import WebMP2CommunityBtn from '@/assets/imgs/webm/p2-community-btn.webm'
import WebMReservationReward1 from '@/assets/imgs/webm/reservation-reward1.webm'
import WebMReservationReward2 from '@/assets/imgs/webm/reservation-reward2.webm'
import WebMReservationReward3 from '@/assets/imgs/webm/reservation-reward3.webm'

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false
})

// 设备检测
const { deviceType } = useDeviceDetection()

// 平台选择 - 根据设备类型初始化
const selectedPlatform = ref<'aos' | 'ios'>(deviceType.value === 'ios' ? 'ios' : 'aos')

// 电话号码相关
const phoneNumber = ref('')

// 应用商店按钮点击处理
const handleStoreClick = (store: string) => {
  console.log(`点击了${store}应用商店`)
}
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
}

.content {
  position: absolute;
  top: 170px;
  margin-left: -50px;
  width: 1239px;
  height: 776px;
  background: url(@/assets/imgs/p2/box-bg.png);
  display: flex;
  align-items: center;
  justify-content: center;

  .rewards {
    position: absolute;
    top: 130px;
    left: 100px;
    display: flex;
    align-items: center;

    .reward {
      margin: 0 -85px;
    }
  }

  .platform-buttons {
    position: absolute;
    top: 470px;
    left: 290px;
    display: flex;
    gap: 80px;
    align-items: center;
    justify-content: center;

    .platform-btn {
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 0;
      width: 53px;
      height: 16px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;

      &.aos-btn {
        background-image: url(@/assets/imgs/p2/aos.png);

        &.active {
          background-image: url(@/assets/imgs/p2/aos-active.png);
        }
      }

      &.ios-btn {
        background-image: url(@/assets/imgs/p2/ios.png);

        &.active {
          background-image: url(@/assets/imgs/p2/ios-active.png);
        }
      }
    }
  }

  .phone-input {
    position: absolute;
    top: 500px;
    left: 170px;
    width: 420px;
  }

  .rule-btn {
    position: absolute;
    top: 556px;
    left: 540px;
    cursor: pointer;
  }

  .reservation-btn {
    position: absolute;
    bottom: 85px;
    left: 235px;
    cursor: pointer;
  }

  .big-reward {
    position: absolute;
    top: 110px;
    right: 70px;
  }

  .store-buttons {
    position: absolute;
    right: 135px;
    top: 375px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    .store-button {
      width: 190px;
      cursor: pointer;
    }
  }

  .community-btn {
    position: absolute;
    bottom: 85px;
    right: 180px;
    cursor: pointer;
  }
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 4rem;
        height: auto;
      }
    }

    .content {
      top: 1.1rem;
      margin: 0 auto;
      margin-left: .2rem;
      width: 7.05rem;
      height: 12.1rem;
      background: url(@/assets/imgs/p2/m/box-bg.png) no-repeat 100%/100%;

      .rewards {
        top: 1.15rem;
        left: 50%;
        transform: translateX(-50%);
        margin-left: -.1rem;

        .reward {
          margin: 0 -0.5rem;
          width: 4rem;
          height: auto;
        }
      }

      .platform-buttons {
        top: 4rem;
        left: 50%;
        transform: translateX(-50%);
        gap: 0.8rem;
        margin-left: -.1rem;

        .platform-btn {
          width: 0.53rem;
          height: 0.16rem;
        }
      }

      .phone-input {
        top: 4.25rem;
        left: 50%;
        width: 4.5rem;
        transform: translateX(-50%);
        margin-left: -.1rem;
      }

      .rule-btn {
        top: 4.85rem;
        left: 5.2rem;

        img {
          width: .5rem;
          height: auto;
        }
      }

      .reservation-btn {
        top: 4.75rem;
        left: 50%;
        height: 1.3rem;
        transform: translateX(-50%);
        margin-left: -.1rem;

        .reservation {
          width: 3rem;
          height: auto;
        }
      }

      .big-reward {
        top: 6rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);

        .reward {
          width: 4.5rem;
          height: auto;
        }
      }

      .store-buttons {
        top: 7.95rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);
        margin-left: -.1rem;
        gap: 0.1rem;

        .store-button {
          width: 1.9rem;
        }
      }

      .community-btn {
        bottom: 0.3rem;
        left: 50%;
        right: unset;
        transform: translateX(-50%);
        margin-left: -.13rem;

        .community {
          width: 3.5rem;
          height: auto;
        }
      }
    }
  }
}
</style>
