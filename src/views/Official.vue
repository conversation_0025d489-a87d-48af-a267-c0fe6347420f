<script lang="ts" setup>
import { ref, onMounted, nextTick, watchEffect } from "vue";
import GameLoading from "@/components/GameLoading.vue";

const htmlContent = ref("");
const loading = ref(true);
const showGameLoading = ref(true);
const error = ref("");
const contentRef = ref<HTMLElement>();
const gameLoadingRef = ref<InstanceType<typeof GameLoading> | null>(null);

// 执行脚本的函数 - 确保所有脚本真正加载完成
const executeScripts = async (container: HTMLElement) => {
  const scripts = container.querySelectorAll("script");
  const isDev = import.meta.env.DEV;
  const scriptPromises: Promise<void>[] = [];

  // 先移除所有旧的脚本标签
  scripts.forEach((script) => script.remove());

  for (let i = 0; i < scripts.length; i++) {
    const oldScript = scripts[i];

    try {
      if (oldScript.src) {
        // 外部脚本：创建 Promise 等待加载完成
        const scriptPromise = new Promise<void>((resolve, reject) => {
          const newScript = document.createElement("script");

          // 处理外部脚本路径
          const src = oldScript.src;
          if (src.startsWith("http")) {
            newScript.src = src;
          } else {
            newScript.src = isDev
              ? `/api/official/${src}` // 开发环境使用代理
              : `https://overbrawl.knetgame.com/official/${src}`; // 生产环境直接请求
          }

          // 复制其他属性
          Array.from(oldScript.attributes).forEach((attr) => {
            if (attr.name !== "src") {
              newScript.setAttribute(attr.name, attr.value);
            }
          });

          // 设置脚本属性以确保执行
          newScript.async = false; // 确保按顺序执行
          newScript.defer = false;

          newScript.onload = () => {
            // 延迟一点时间确保脚本完全执行和初始化
            setTimeout(() => resolve(), 100);
          };
          newScript.onerror = (err) => {
            resolve(); // 继续执行其他脚本
          };

          // 添加到 head
          document.head.appendChild(newScript);
        });

        scriptPromises.push(scriptPromise);
      } else if (oldScript.innerHTML.trim()) {
        // 内联脚本：使用 eval 或 Function 执行
        const scriptContent = oldScript.innerHTML.trim();

        try {
          // 方法1：使用 eval 在全局作用域执行
          (0, eval)(scriptContent);
        } catch (evalErr) {
          try {
            // 方法2：使用 Function 构造器
            const func = new Function(scriptContent);
            func.call(window);
          } catch (funcErr) {

            // 方法3：创建新的 script 标签添加到 head
            const newScript = document.createElement("script");
            newScript.type = "text/javascript";
            newScript.text = scriptContent; // 使用 text 而不是 innerHTML

            // 复制其他属性
            Array.from(oldScript.attributes).forEach((attr) => {
              if (attr.name !== "src") {
                newScript.setAttribute(attr.name, attr.value);
              }
            });

            document.head.appendChild(newScript);

            // 立即移除以避免重复执行
            setTimeout(() => {
              if (newScript.parentNode) {
                newScript.parentNode.removeChild(newScript);
              }
            }, 100);
          }
        }
      }
    } catch (err) {
      // 静默处理脚本错误
    }
  }

  // 等待所有外部脚本加载完成
  if (scriptPromises.length > 0) {
    await Promise.all(scriptPromises);
  }

  // 触发常见的页面初始化事件
  try {
    // 触发 DOMContentLoaded 事件（如果脚本依赖此事件）
    const domContentLoadedEvent = new Event("DOMContentLoaded", {
      bubbles: true,
      cancelable: true,
    });
    document.dispatchEvent(domContentLoadedEvent);

    // 触发 load 事件
    const loadEvent = new Event("load", {
      bubbles: true,
      cancelable: true,
    });
    window.dispatchEvent(loadEvent);

    // 如果有 jQuery，触发 ready 事件
    if (typeof (window as any).$ !== "undefined" && (window as any).$.fn) {
      (window as any).$(document).ready(() => {});
      (window as any).$(document).trigger("ready");
    }

    // 等待一段时间让脚本完全初始化
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 尝试调用一些常见的初始化函数
    const commonInitFunctions = [
      "init",
      "initialize",
      "start",
      "main",
      "onLoad",
    ];
    commonInitFunctions.forEach((funcName) => {
      if (typeof (window as any)[funcName] === "function") {
        try {
          (window as any)[funcName]();
        } catch (err) {}
      }
    });
  } catch (err) {
    // 静默处理初始化事件错误
  }
};

// 处理样式表
const processStylesheets = (html: string) => {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, "text/html");
  const isDev = import.meta.env.DEV;

  // 处理外部样式表
  const links = doc.querySelectorAll('link[rel="stylesheet"]');
  links.forEach((link) => {
    const href = link.getAttribute("href");
    if (href && !document.querySelector(`link[href="${href}"]`)) {
      const newLink = document.createElement("link");
      newLink.rel = "stylesheet";

      // 根据环境处理 URL
      if (href.startsWith("http")) {
        newLink.href = href;
      } else {
        newLink.href = isDev
          ? `/api/official/${href}` // 开发环境使用代理
          : `https://overbrawl.knetgame.com/official/${href}`; // 生产环境直接请求
      }

      document.head.appendChild(newLink);
    }
  });

  return html;
};

// 处理meta标签 - 从获取的HTML中提取
const processMetaTags = (html: string) => {
  try {
    // 清除现有的meta标签（除了基础的viewport和charset）
    const existingMetas = document.querySelectorAll(
      'meta:not([charset]):not([name="viewport"])'
    );
    existingMetas.forEach((meta) => meta.remove());

    // 从HTML中解析meta标签
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    // 获取所有meta标签（排除charset和viewport）
    const metaTags = doc.querySelectorAll(
      'meta:not([charset]):not([name="viewport"])'
    );

    // 添加新的meta标签到当前页面
    metaTags.forEach((meta) => {
      const newMeta = document.createElement("meta");
      Array.from(meta.attributes).forEach((attr) => {
        newMeta.setAttribute(attr.name, attr.value);
      });
      document.head.appendChild(newMeta);
    });
  } catch (err) {
    // 静默处理错误，不影响主要功能
  }
};

onMounted(async () => {
  try {
    // 根据环境选择请求地址
    const isDev = import.meta.env.DEV;
    const url = isDev
      ? "/api/official/index.html" // 开发环境使用代理
      : "/official/index.html"; // 生产环境直接请求

    // 获取 HTML 文件内容
    const response = await fetch(url, {
      method: "GET",
      mode: isDev ? "same-origin" : "cors", // 开发环境同源，生产环境跨域
      credentials: "omit", // 不发送凭据
      headers: {
        Accept:
          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    let html = await response.text();

    // 处理meta标签
    processMetaTags(html);

    // 处理样式表
    html = processStylesheets(html);

    // 提取内容（包含 head 中的脚本和 body 内容）
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, "text/html");

    // 获取所有脚本（head 和 body 中的）
    const allScripts = doc.querySelectorAll("script");
    // 获取 body 内容
    const bodyContent = doc.body?.innerHTML || "";

    // 将所有脚本添加到 body 内容中
    let scriptsHtml = "";
    allScripts.forEach((script) => {
      scriptsHtml += script.outerHTML;
    });

    const finalContent = scriptsHtml + bodyContent;

    htmlContent.value = finalContent;
    loading.value = false; // 先设置加载完成状态

    // 内容加载完成，但保持加载动画直到脚本执行完成
  } catch (err) {
    error.value = `加载内容失败: ${err instanceof Error ? err.message : "未知错误"}`;
    loading.value = false;
    showGameLoading.value = false;
  }
});

// 监听内容加载完成，完成进度条但保持加载动画
watchEffect(async () => {
  if (htmlContent.value && !loading.value && !error.value) {
    // 完成进度条
    if (gameLoadingRef.value) {
      gameLoadingRef.value.completeProgress();
    }
  }
});

// 监听 contentRef 变化，当 DOM 元素存在时执行脚本并隐藏加载动画
watchEffect(async () => {
  if (contentRef.value && htmlContent.value && !loading.value && !error.value) {
    await nextTick(); // 确保 DOM 完全更新

    try {
      // 执行所有脚本
      await executeScripts(contentRef.value);

      // 所有脚本执行完成后，稍微延迟再隐藏加载动画
      setTimeout(() => {
        showGameLoading.value = false;
      }, 200); // 脚本已经完全加载，只需要很短的延迟
    } catch (error) {
      // 即使出错也要隐藏加载动画
      setTimeout(() => {
        showGameLoading.value = false;
      }, 500);
    }
  }
});
</script>

<template>
  <div class="official">
    <GameLoading v-if="showGameLoading" ref="gameLoadingRef" />
    <div v-if="error && !showGameLoading" class="error">
      {{ error }}
    </div>
    <div
      v-if="htmlContent && !error"
      ref="contentRef"
      v-html="htmlContent"
      class="content"
      :style="{ display: showGameLoading ? 'none' : 'block' }"
    ></div>
  </div>
</template>

<style scoped lang="less">
.official {
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: auto;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 18px;
  color: #ff4444;
}

.content {
  width: 100%;
  min-height: 100vh; // 最小高度为视口高度
  height: auto; // 高度自适应内容

  // 确保内嵌的 HTML 内容样式正常
  :deep(*) {
    box-sizing: border-box;
  }

  // 重置可能的样式冲突
  :deep(body) {
    margin: 0;
    padding: 0;
  }

  // 确保外部页面内容能够完整显示
  :deep(html),
  :deep(body) {
    height: auto !important;
    min-height: 100vh;
  }
}
</style>
