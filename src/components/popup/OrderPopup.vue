<template>
  <Popup ref="popupRules">
    <div class="bg">
      <div class="close"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import Popup from './Popup.vue';

const popupRules = ref<InstanceType<typeof Popup> | null>(null);

const open = () => {
  popupRules.value?.open();
};

onMounted(() => {
  open()
})

defineExpose({
  open
})
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 653px;
  height: 832px;
  background: url("@/assets/imgs/pop/order/bg.png") no-repeat;
  background-size: 100% 100%;

  .close {
    position: absolute;
    top: -30px;
    right: 30px;
    width: 60px;
    height: 59px;
    background: url("@/assets/imgs/pop/order/close.png") no-repeat;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #b294bc);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 90vw;
    height: calc(90vw * 0.873);
    max-width: 400px;
    max-height: 349px;

    .btn {
      width: 41%;
      height: calc(41% * 0.233);
      background-size: 100% 100%;
    }
  }
}
</style>
