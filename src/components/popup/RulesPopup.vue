<template>
  <Popup ref="popupRules">
    <div class="bg">
      <div class="btn close"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Popup from './Popup.vue';

const popupRules = ref<InstanceType<typeof Popup> | null>(null);

const open = () => {
  popupRules.value?.open();
};

defineExpose({
  open
})
</script>

<style scoped lang="less">
.bg {
  display: flex;
  justify-content: center;
  width: 720px;
  height: 629px;
  background: url("@/assets/imgs/pop/rules/bg.png") no-repeat;
  background-size: 100% 100%;

  .btn {
    position: absolute;
    bottom: 10%;
    width: 296px;
    height: 69px;
    background: url("@/assets/imgs/pop/rules/btn.png") no-repeat;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 90vw;
    height: calc(90vw * 0.873);
    max-width: 400px;
    max-height: 349px;

    .btn {
      width: 41%;
      height: calc(41% * 0.233);
      background-size: 100% 100%;
    }
  }
}
</style>
