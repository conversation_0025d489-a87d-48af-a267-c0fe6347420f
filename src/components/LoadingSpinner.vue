<template>
    <div class="spinner-container">
        <div class="spinner"></div>
    </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.2);
    z-index: 9999;
}

.spinner {
    width: 0.8rem;
    height: 0.8rem;
    border: 0.2rem solid transparent;
    /* 边框宽度 */
    border-top-color: #59dbc8;
    border-right-color: #59db96;
    border-bottom-color: #59db96;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    animation: spin 1s infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
        /* 完整旋转 */
    }
}
</style>