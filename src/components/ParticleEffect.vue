<template>
  <WebM 
    v-if="visible" 
    class="particle-effect" 
    :class="customClass"
    :style="particleStyle" 
    :src="WebMParticle" 
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import WebM from '@/components/WebM.vue'

// WebM 资源导入
import WebMParticle from '@/assets/imgs/webm/particle.webm'

// Props 定义
interface Props {
  /** 是否显示粒子效果 */
  visible?: boolean
  /** 自定义 CSS 类名 */
  customClass?: string
  /** z-index 层级 */
  zIndex?: number
  /** 自定义位置 - top */
  top?: string
  /** 自定义位置 - left */
  left?: string
  /** 自定义位置 - right */
  right?: string
  /** 自定义位置 - bottom */
  bottom?: string
  /** 自定义 transform */
  transform?: string
  /** 自定义宽度 */
  width?: string
  /** 自定义高度 */
  height?: string
  /** 透明度 */
  opacity?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  customClass: '',
  zIndex: 0,
  top: '50%',
  left: '50%',
  right: undefined,
  bottom: undefined,
  transform: 'translate(-50%, -50%)',
  width: undefined,
  height: undefined,
  opacity: 1
})

// 计算样式
const particleStyle = computed(() => {
  const style: Record<string, string | number> = {
    zIndex: props.zIndex,
    opacity: props.opacity
  }

  // 位置设置
  if (props.top !== undefined) style.top = props.top
  if (props.left !== undefined) style.left = props.left
  if (props.right !== undefined) style.right = props.right
  if (props.bottom !== undefined) style.bottom = props.bottom
  if (props.transform !== undefined) style.transform = props.transform

  // 尺寸设置
  if (props.width !== undefined) style.width = props.width
  if (props.height !== undefined) style.height = props.height

  return style
})
</script>

<style lang="less" scoped>
.particle-effect {
  position: absolute;
  pointer-events: none; // 不阻挡用户交互
}
</style>
