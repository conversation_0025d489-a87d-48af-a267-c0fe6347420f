<template>
  <div v-if="isDev" class="clear-data-button" @click="clearAllData">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path
        d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M10 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M14 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
    <span>清除数据</span>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/stores/app";

const appStore = useAppStore();
const { isDev } = appStore;

const clearAllData = () => {
  try {
    // 停止倒计时
    appStore.stopCountdown();

    // 清除 localStorage
    localStorage.clear();

    // 清除所有 cookies
    document.cookie.split(";").forEach((cookie) => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      if (name) {
        // 清除当前域名下的 cookie
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        // 清除当前域名和子域名下的 cookie
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
        // 清除父域名下的 cookie
        const hostParts = window.location.hostname.split(".");
        if (hostParts.length > 1) {
          const parentDomain = "." + hostParts.slice(-2).join(".");
          document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${parentDomain}`;
        }
      }
    });

    // 清除 sessionStorage
    sessionStorage.clear();

    // 刷新页面
    window.location.reload();
  } catch (error) {
    console.error("清除数据时出错:", error);
    // 即使出错也尝试刷新页面
    window.location.reload();
  }
};
</script>

<style scoped>
.clear-data-button {
  position: fixed;
  top: 50%;
  right: 0px;
  transform: translateY(-50%);
  z-index: 2;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

.clear-data-button:hover {
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  background: linear-gradient(135deg, #ff5252, #d63031);
}

.clear-data-button:active {
  transform: translateY(-50%) scale(0.95);
}

.clear-data-button svg {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.clear-data-button span {
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 540px) {
  .clear-data-button {
    padding: 10px 16px;
    font-size: 12px;
  }

  .clear-data-button svg {
    width: 16px;
    height: 16px;
  }
}
</style>
