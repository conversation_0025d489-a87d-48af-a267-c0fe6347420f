<template>
  <div class="right-floating-menu">
    <!-- 图片轮播区域 -->
    <div class="carousel-section">
      <BaseSwiper :autoplay="{ delay: 4000, disableOnInteraction: false }" :loop="true" :modules="modules"
        class="prize-swiper">
        <swiper-slide v-for="(image, index) in prizeImages" :key="index">
          <img :src="image" alt="奖品轮播" class="carousel-image" />
        </swiper-slide>
      </BaseSwiper>
    </div>

    <!-- 应用商店按钮 -->
    <div class="app-store-section">
      <img :src="storeButtonImage" alt="应用商店" class="btn store-button" @click="handleStoreClick" />
    </div>

    <!-- 活动按钮 -->
    <div class="activity-section">
      <img src="@/assets/imgs/right-menu/activity.png" alt="活动" class="btn activity-button"
        @click="handleActivityClick" />
    </div>

    <!-- Naver按钮 -->
    <div class="naver-section">
      <img src="@/assets/imgs/right-menu/naver.png" alt="Naver" class="btn naver-button" @click="handleNaverClick" />
    </div>

    <!-- 预约按钮 -->
    <div class="reservation-section">
      <img src="@/assets/imgs/right-menu/reservation.png" alt="预约" class="btn reservation-button"
        @click="handleReservationClick" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { SwiperSlide } from 'swiper/vue'
import { Autoplay, EffectFade } from 'swiper/modules'
import BaseSwiper from '@/components/swiper/BaseSwiper.vue'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { useSwiper } from '@/hooks/useSwiper'

// Props 接口定义
interface Props {
  mainSwiperInstance?: any // 主swiper实例，用于导航
}

const props = defineProps<Props>()

// 设备检测
const { deviceType } = useDeviceDetection()

const { modules } = useSwiper([Autoplay, EffectFade])

// 批量导入奖品图片
const prizeImagesRaw = import.meta.glob('@/assets/imgs/right-menu/prize/*.png', { eager: true })
const prizeImages = computed(() => {
  return Object.values(prizeImagesRaw).map((item: any) => item.default)
})

// 根据设备类型动态显示应用商店按钮
const storeButtonImage = computed(() => {
  if (deviceType.value === 'ios') {
    return new URL('../assets/imgs/p1/apple-store.png', import.meta.url).href
  } else {
    return new URL('../assets/imgs/p1/google-play.png', import.meta.url).href
  }
})

// 应用商店按钮点击处理
const handleStoreClick = () => {
  console.log(`点击了${deviceType.value === 'ios' ? 'Apple Store' : 'Google Play'}`)
  // TODO: 实现应用商店跳转逻辑
}

// 活动按钮点击处理 - 导航到第三屏
const handleActivityClick = () => {
  if (props.mainSwiperInstance) {
    props.mainSwiperInstance.slideTo(2) // 导航到第3屏（索引为2）
  }
}

// Naver按钮点击处理
const handleNaverClick = () => {
  console.log('点击了Naver按钮')
  // TODO: 实现Naver相关逻辑
}

// 预约按钮点击处理 - 导航到第二屏
const handleReservationClick = () => {
  if (props.mainSwiperInstance) {
    props.mainSwiperInstance.slideTo(1) // 导航到第2屏（索引为1）
  }
}
</script>

<style lang="less" scoped>
.right-floating-menu {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  background: url('@/assets/imgs/right-menu/bg.png') no-repeat;
  background-size: contain;
  width: 311.5px;
  height: 318.5px;
  display: flex;
  flex-direction: column;
  align-items: center;

  // 轮播区域
  .carousel-section {
    width: 180px;
    height: auto;
    position: relative;
    margin: 0 auto;

    .carousel-image {
      width: 100%;
      object-fit: contain;
      cursor: pointer;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .btn {
    width: 130px;
    height: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 5px;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1);
    }
  }

  // 预约按钮
  .reservation-section {
    .reservation-button {
      width: 200px;
    }
  }

  @media (max-width: 540px) {
    display: none; // 在移动端隐藏
  }
}
</style>
