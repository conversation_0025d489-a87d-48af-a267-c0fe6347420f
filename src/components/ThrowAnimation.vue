<template>
  <div class="throw-animation" v-if="isPlaying">
    <img 
      :key="animationKey" 
      :src="gifSrc" 
      @load="onImageLoad"
      @error="onImageError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';

interface Props {
  isPlaying: boolean;
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 2000
});

const emit = defineEmits<{
  animationEnd: [];
}>();

const animationKey = ref(0);
const timeoutId = ref<number | null>(null);

// 使用import.meta.url获取正确的资源路径
const gifSrc = computed(() => {
  return new URL('../assets/imgs/p3/throw.gif', import.meta.url).href;
});

// 图片加载成功
const onImageLoad = () => {
  // 设置动画结束定时器
  if (timeoutId.value) {
    clearTimeout(timeoutId.value);
  }
  
  timeoutId.value = window.setTimeout(() => {
    emit('animationEnd');
  }, props.duration);
};

// 图片加载失败
const onImageError = () => {
  console.error('Failed to load throw animation GIF');
  emit('animationEnd');
};

// 监听播放状态变化
watch(() => props.isPlaying, (newVal) => {
  if (newVal) {
    // 每次播放时更新key，强制重新渲染img元素
    animationKey.value++;
  } else {
    // 停止播放时清除定时器
    if (timeoutId.value) {
      clearTimeout(timeoutId.value);
      timeoutId.value = null;
    }
  }
});
</script>

<style scoped>
.throw-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.throw-animation img {
  display: block;
  width: 200px;
  height: 200px;
  object-fit: contain;
}
</style>