<template>
  <header v-if="!isMobile" class="header-container">
    <div class="header-content">
      <!-- Logo 左侧 -->
      <div class="header-left">
        <img src="@/assets/imgs/header/logo.png" alt="Logo" class="logo" @click="handleLogoClick" />
      </div>

      <!-- 按钮组 右侧 -->
      <div class="header-right">
        <img src="@/assets/imgs/header/reward.png" alt="奖励" class="nav-button reward" @click="handleRewardClick" />
        <img src="@/assets/imgs/header/community.png" alt="社区" class="nav-button community"
          @click="handleCommunityClick" />
        <img src="@/assets/imgs/header/youtube.png" alt="YouTube" class="nav-button youtube"
          @click="handleYouTubeClick" />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { useIsMobile } from '@/hooks/useIsMobile';

// 定义emit事件
const emit = defineEmits<{
  logoClick: []
  rewardClick: []
  communityClick: []
  youtubeClick: []
}>()

const { isMobile } = useIsMobile()

const handleLogoClick = () => {
  console.log('Logo clicked - 返回首页')
  emit('logoClick')
}

const handleRewardClick = () => {
  console.log('奖励按钮点击')
  emit('rewardClick')
}

const handleCommunityClick = () => {
  console.log('社区按钮点击')
  emit('communityClick')
}

const handleYouTubeClick = () => {
  console.log('YouTube按钮点击')
  emit('youtubeClick')
}
</script>

<style lang="less" scoped>
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  z-index: 10;
  padding: 16px 0;
  pointer-events: none;
}

.header-content {
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  pointer-events: auto;
}

.logo {
  width: 244px;
  height: auto;
  cursor: pointer;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.header-right {
  display: flex;
  align-items: center;
  pointer-events: auto;
}

.nav-button {
  height: auto;
  cursor: pointer;
  transition: all 0.3s ease;

  &.reward {
    width: 79px;
  }

  &.community {
    width: 67px;
  }

  &.youtube {
    width: 62px;
  }

  &:hover {
    transform: translateY(-2px);
    filter: drop-shadow(0 4px 8px rgba(255, 255, 255, 0.2));
  }

  &:active {
    transform: translateY(0);
  }
}
</style>