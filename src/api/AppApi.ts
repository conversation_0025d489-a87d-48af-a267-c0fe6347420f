import type { EventInit } from "@/stores/app";
import { BaseApi } from "./Base/BaseApi";

export type ToOrderParams = {
  warm_page_en_name?: string;
  phone?: string;
  dialing_code?: string;
  code?: string;
  recommend_utm?: string;
  version?: string;
  page?: string;
};

export default class AppApi extends BaseApi {
  static getWarmPage(params: any) {
    const url = `/website/warmPage/index`;
    const data = {
      ...params,
    };
    return super.get<WarmPageConfig>(url, data);
  }

  /**
   * 去预约
   * @param params
   * @returns
   */
  static toOrder(params: ToOrderParams) {
    const url = `/website/order/order`;
    const data = {
      ...params,
    };
    return super.get<OrderResult>(url, data);
  }

  /**
   * 增加抽奖次数
   * @param params
   * @returns
   */
  static addLuckyCnt(params: any) {
    const url = `/website/lottery/addLuckyCnt`;
    const data = {
      ...params,
    };
    return super.get<any>(url, data);
  }

  /**
   * 增加抽奖次数
   * @param params
   * @returns
   */
  static lottery(params: any) {
    const url = `/website/lottery/lottery`;
    const data = {
      ...params,
    };
    return super.get<any>(url, data);
  }

  /**
   * 领取礼包
   * @param params
   * @returns
   */
  static grant(params: any) {
    const url = `/website/packageGrant/grant`;
    const data = {
      ...params,
    };
    return super.get<any>(url, data);
  }

  /**
   * 获取埋点初始化
   * @param params
   * @returns
   */
  static getEventInit(params: any) {
    const url = `/website/event/getEventArgs`;
    const data = {
      ...params,
    };
    return super.get<EventInit>(url, data);
  }

  /**
   * 统计后台埋点接口
   * @param parmas
   * @returns
   */
  static reportEvent(parmas: any) {
    const url = `/website/event/report`;
    const data = {
      ...parmas,
    };
    return super.get<EventInit>(url, data);
  }
}
