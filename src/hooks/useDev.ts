import { useAppStore } from "@/stores/app";
import { storeToRefs } from "pinia";
import { onMounted } from "vue";
import { useRoute } from "vue-router";

export const useDev = () => {
  const route = useRoute();
  const appStore = useAppStore();
  const { isDev } = storeToRefs(appStore);

  onMounted(() => {
    const isTest = route.query.isTest;
    if (isTest != "1" && isDev.value) {
      //清除documtn内容
      return (document.body.innerHTML = "");
    }
  });
};
