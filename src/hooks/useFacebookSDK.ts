import { ref, onMounted, onUnmounted } from "vue";

interface FacebookSDKConfig {
  appId: string;
  xfbml?: boolean;
  version?: string;
  locale?: string;
}

interface FacebookSDK {
  init: (config: any) => void;
  getLoginStatus: (callback: (response: any) => void) => void;
  login: (callback: (response: any) => void, options?: any) => void;
  logout: (callback: (response: any) => void) => void;
  api: (path: string, callback: (response: any) => void) => void;
  ui: (params: any, callback: (response: any) => void) => void;
  AppEvents: {
    logEvent: (
      eventName: string,
      valueToSum?: number,
      parameters?: any
    ) => void;
    logPageView: () => void;
  };
}

declare global {
  interface Window {
    FB?: FacebookSDK;
    fbAsyncInit?: () => void;
  }
}

export function useFacebookSDK(config: FacebookSDKConfig) {
  //如果是facebook渠道，加载插件会和渠道插件冲突，先屏蔽，分享用链接跳转方式
  const defaultConfig = {
    xfbml: true,
    version: "v23.0",
    locale: "zh_HK",
    ...config,
  };

  const loadSDK = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (window.FB) {
        resolve();
        return;
      }

      // 检查是否已经有Facebook SDK脚本标签（只检查sdk.js，不检查fbevents.js）
      const existingScript = document.querySelector(
        'script[src*="connect.facebook.net"][src*="sdk.js"]'
      );
      if (existingScript) {
        // 等待现有脚本加载完成
        existingScript.addEventListener("load", () => resolve());
        existingScript.addEventListener("error", () =>
          reject(new Error("Failed to load Facebook SDK"))
        );
        return;
      }

      // 设置异步初始化函数
      window.fbAsyncInit = function () {
        if (window.FB) {
          // 在HTTP环境下禁用xfbml以避免自动调用getLoginStatus
          const initConfig = {
            appId: defaultConfig.appId,
            xfbml:
              window.location.protocol === "https:"
                ? defaultConfig.xfbml
                : false,
            version: defaultConfig.version,
          };
          window.FB.init(initConfig);
          resolve();
        }
      };

      // 动态加载 Facebook SDK
      const script = document.createElement("script");
      script.async = true;
      script.defer = true;
      script.crossOrigin = "anonymous";
      script.src = `https://connect.facebook.net/${defaultConfig.locale}/sdk.js`;

      script.onerror = () =>
        reject(new Error("Failed to load Facebook SDK script"));

      // 插入到页面中
      const firstScript = document.getElementsByTagName("script")[0];
      if (firstScript && firstScript.parentNode) {
        firstScript.parentNode.insertBefore(script, firstScript);
      } else {
        document.head.appendChild(script);
      }
    });
  };

  const initSDK = async () => {
    try {
      if (window.location.href.includes("facebook")) {
        return;
      }
      await loadSDK();

      // 如果 SDK 已加载但未初始化，手动初始化
      if (window.FB) {
        window.FB.init({
          appId: defaultConfig.appId,
          xfbml: defaultConfig.xfbml,
          version: defaultConfig.version,
        });
      }
    } catch (err) {
      console.error("Facebook SDK initialization failed:", err);
    }
  };

  const getFbSDK = (): FacebookSDK | null => {
    return window.FB || null;
  };

  const safeCallFbAPI = <T extends any[]>(
    method: string,
    ...args: T
  ): boolean => {
    const FB = getFbSDK();
    if (!FB) {
      console.warn("Facebook SDK not loaded");
      return false;
    }

    try {
      (FB as any)[method](...args);
      return true;
    } catch (error) {
      console.error(`Facebook ${method} API error:`, error);
      return false;
    }
  };

  const cleanup = () => {
    // 清理全局变量
    if (window.fbAsyncInit) {
      delete window.fbAsyncInit;
    }
  };

  onMounted(() => {
    initSDK();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    getFbSDK,
    initSDK,
    cleanup,
    safeCallFbAPI,
  };
}

// 导出类型供其他地方使用
export type { FacebookSDKConfig, FacebookSDK };
