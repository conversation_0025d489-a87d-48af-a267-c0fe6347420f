import { computed, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'

/**
 * 活动倒计时 Hook
 * 基于服务器时间戳的长期活动倒计时，避免时区问题
 */
export function useActivityCountdown() {
  const appStore = useAppStore()
  const { countdown, isOver, endTime } = storeToRefs(appStore)

  // 格式化倒计时显示
  const formattedCountdown = computed(() => {
    if (!countdown.value) return null
    
    return {
      days: String(countdown.value.days).padStart(2, '0'),
      hours: String(countdown.value.hours).padStart(2, '0'),
      minutes: String(countdown.value.minutes).padStart(2, '0'),
      seconds: String(countdown.value.seconds).padStart(2, '0')
    }
  })

  // 倒计时文本显示
  const countdownText = computed(() => {
    if (!formattedCountdown.value) return ''
    
    const { days, hours, minutes, seconds } = formattedCountdown.value
    return `${days}天 ${hours}:${minutes}:${seconds}`
  })

  // 是否显示倒计时
  const showCountdown = computed(() => {
    return !isOver.value && !!countdown.value
  })

  // 更新倒计时（可选传入服务器时间戳）
  const updateCountdown = (currentServerTime?: number) => {
    appStore.updateCountdown(currentServerTime)
  }

  // 开始倒计时动画
  const startCountdown = () => {
    appStore.startCountdown()
  }

  // 停止倒计时动画
  const stopCountdown = () => {
    appStore.stopCountdown()
  }



  // 组件卸载时自动停止倒计时
  onUnmounted(() => {
    stopCountdown()
  })

  return {
    // 状态
    countdown,
    isOver,
    endTime,
    
    // 计算属性
    formattedCountdown,
    countdownText,
    showCountdown,
    
    // 方法
    updateCountdown,
    startCountdown,
    stopCountdown
  }
}