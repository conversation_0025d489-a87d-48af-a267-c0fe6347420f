import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";

export const useUrlParams = () => {
  const route = useRoute();
  const recommend_code = ref("");
  const recommend_utm = ref("");

  onMounted(() => {
    recommend_code.value = decodeURIComponent(
      (route.query.code as string) || ""
    );
    recommend_utm.value = (route.params.recommend_utm as string) || "other";
  });

  return {
    recommend_code,
    recommend_utm,
  };
};
