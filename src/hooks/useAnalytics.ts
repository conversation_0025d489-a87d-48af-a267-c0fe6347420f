// src/hooks/useAnalytics.ts
import { getCommonGa4Config } from "@/config/platformConfig";
import { onMounted } from "vue";
import { useRoute } from "vue-router";

export type AnalyticsChannel = "meta" | "tiktok" | "ga4Utm" | "line" | "ga4";

export interface ChannelConfig {
  type: AnalyticsChannel;
  id: string;
}

export interface PlatformMap {
  [key: string]: {
    channel: AnalyticsChannel;
    id: string;
    params?: any;
    eventName: string;
    storeClick?: any;
    ios_click_fb?: string;
    gp_click_fb?: string;
    ios_cpp_link?: string;
    gp_link?: string;
  };
}

export function useAnalytics(pixelMap: PlatformMap) {
  const route = useRoute();

  // 渠道初始化映射
  const initializers: Record<AnalyticsChannel, (id: string) => void> = {
    meta: initMetaPixel,
    tiktok: initTikTokPixel,
    ga4Utm: initGoogleAnalytics,
    line: initLinePixel,
    ga4: initGoogleAnalytics,
  };

  // Meta Pixel初始化
  function initMetaPixel(pixelId: string) {
    if (!window.fbq) {
      (function (f: any, b: any, e: any) {
        var n = f.fbq;
        if (n) return n.callMethod;
        n = f.fbq = function () {
          n.callMethod
            ? n.callMethod.apply(n, arguments)
            : n.queue.push(arguments);
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = "2.0";
        n.queue = [];
        var t = b.createElement(e);
        t.async = !0;
        t.src = "https://connect.facebook.net/zh_HK/fbevents.js";
        var s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s);
      })(window, document, "script");
      // Initialize Facebook Pixel with the provided pixel ID
      window.fbq("init", pixelId);
      window.fbq("track", "PageView");
    }
  }

  // TikTok Pixel初始化
  function initTikTokPixel(pixelId: string) {
    window.TiktokAnalyticsObject = "ttq";
    const ttq = (window.ttq = window.ttq || []);

    // 方法队列初始化
    const methods = [
      "page",
      "track",
      "identify",
      "instances",
      "debug",
      "on",
      "off",
      "once",
      "ready",
      "alias",
      "group",
      "enableCookie",
      "disableCookie",
      "holdConsent",
      "revokeConsent",
      "grantConsent",
    ];

    methods.forEach((method) => {
      ttq[method] = (...args: any) => ttq.push([method, ...args]);
    });

    // 动态加载脚本
    if (!document.querySelector(`script[src*="analytics.tiktok.com"]`)) {
      const script = document.createElement("script");
      script.src = `https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=${pixelId}&lib=ttq`;
      document.head.appendChild(script);
    }

    ttq.page(); // 触发页面跟踪
  }

  // Line 初始化
  function initLinePixel(pixelId: string) {
    // 检查是否已经初始化过 Line Tag
    if (!document.querySelector('script[src*="line_tag"]')) {
      // 初始化全局对象
      window._ltq = window._ltq || [];
      if (!window._lt) {
        window._lt = function () {
          window._ltq.push(arguments);
        };
      }

      // 动态加载 Line Tag 脚本
      const protocol =
        location.protocol === "https:"
          ? "https://d.line-scdn.net"
          : "http://d.line-cdn.net";
      const script = document.createElement("script");
      script.async = true;
      script.src = `${protocol}/n/line_tag/public/release/v1/lt.js`;
      document.head.appendChild(script);
    }

    // 初始化 Line Tag
    window._lt("init", {
      customerType: "lap",
      tagId: pixelId,
    });
    window._lt("send", "pv", [pixelId]);
  }

  // GA4初始化
  function initGoogleAnalytics(ga4Id: string) {
    const script = document.createElement("script");
    script.src = `https://www.googletagmanager.com/gtag/js?id=${ga4Id}`;
    script.async = true;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    if (!window.gtag) {
      window.gtag = function () {
        window.dataLayer.push(arguments);
      };
    }
    window.gtag("js", new Date());
    window.gtag("config", ga4Id, {
      send_to: ga4Id,
    });
  }

  // 统一事件跟踪
  const trackEvent = (
    type: AnalyticsChannel,
    eventName: string,
    params: Record<string, any> = {}
  ) => {
    switch (type) {
      case "meta":
        window.fbq?.("track", eventName, params);
        break;
      case "tiktok":
        window.ttq?.track(eventName, params);
        break;
      case "ga4Utm":
        window.gtag?.("event", eventName, params);
        break;
      case "ga4":
        window.gtag?.("event", eventName, params);
        break;
      case "line":
        window._lt(
          "send",
          "cv",
          {
            type: eventName,
          },
          params
        );
        break;
    }
  };

  const ga4TrackEvent = (eventName: string) => {
    trackEvent("ga4", eventName, {
      send_to: getCommonGa4Config().id,
    });
  };

  // 初始化渠道
  const initializeChannels = () => {
    //初始化ga4跟踪代码
    const ga4Config = getCommonGa4Config();
    if (ga4Config) {
      initGoogleAnalytics(ga4Config.id);
    }
    const page = route.name as string;
    const utm = ((route.params?.recommend_utm as string) || "").toLowerCase();
    if (!utm) {
      return;
    }
    const channelConfig = pixelMap[`${page}_${utm}`];
    if (channelConfig) {
      initializers[channelConfig.channel](channelConfig.id);
    }
  };

  const getChannelConfig = () => {
    const page = route.name as string;
    const utm = ((route.params?.recommend_utm as string) || "").toLowerCase();
    if (!utm) {
      return;
    }
    let config = pixelMap[`${location.host}_${page}_${utm}`];
    if (!config) {
      config = pixelMap[`${page}_${utm}`];
    }
    return config;
  };

  onMounted(() => {
    initializeChannels();
  });

  return {
    trackEvent,
    ga4TrackEvent,
    getChannelConfig,
  };
}
