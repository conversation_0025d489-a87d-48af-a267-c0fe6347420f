import { onMounted, nextTick } from "vue";

// 预加载阶段配置接口
interface PreloadStage {
  delay: number; // 延迟时间（毫秒）
  assets?: string[]; // 该阶段要预加载的资源
  blacklist?: string[]; // 该阶段的黑名单
  batchSize?: number; // 该阶段的批次大小
  priority?: "high" | "low"; // 该阶段的优先级
}

export function usePreloadAssets(
  assets?: string[],
  options?: {
    delay?: number;
    batchSize?: number;
    priority?: "high" | "low";
    blacklist?: string[]; // 过滤名单，在名单内的资源不进行预加载
    stages?: PreloadStage[]; // 多阶段预加载配置
  }
) {
  const {
    delay = 2000,
    batchSize = 5,
    priority = "low",
    blacklist = [],
    stages = [],
  } = options || {};

  onMounted(() => {
    // 等待首页完全渲染完成
    nextTick(() => {
      if (stages.length > 0) {
        // 使用多阶段预加载
        startMultiStagePreloading();
      } else {
        // 使用传统单阶段预加载
        setTimeout(() => {
          startPreloading();
        }, delay);
      }
    });
  });

  const startMultiStagePreloading = () => {
    // 检查网络状态和设备性能
    if (!shouldPreload()) {
      console.log("跳过预加载：网络或设备条件不佳");
      return;
    }

    // 为每个阶段设置定时器
    stages.forEach((stage, index) => {
      setTimeout(() => {
        console.log(`开始第${index + 1}阶段预加载，延迟${stage.delay}ms`);
        startStagePreloading(stage, index + 1);
      }, stage.delay);
    });
  };

  const startStagePreloading = (stage: PreloadStage, stageNumber: number) => {
    // 获取该阶段的资源
    const stageAssets = stage.assets || assets || Object.values(
      import.meta.glob("/src/assets/imgs/**/*.{jpg,png,svg,mp4,webp}")
    );

    // 合并全局黑名单和阶段黑名单
    const combinedBlacklist = [...blacklist, ...(stage.blacklist || [])];

    // 过滤掉黑名单中的资源
    const assetsToPreload = filterAssetsByBlacklist(stageAssets, combinedBlacklist);

    if (assetsToPreload.length === 0) {
      console.log(`第${stageNumber}阶段没有需要预加载的资源`);
      return;
    }

    console.log(`第${stageNumber}阶段开始预加载 ${assetsToPreload.length} 个资源`);

    // 分批预加载资源
    batchPreloadAssets(
      assetsToPreload,
      stage.batchSize || batchSize,
      stage.priority || priority,
      stageNumber
    );
  };

  const startPreloading = () => {
    // 检查网络状态和设备性能
    if (!shouldPreload()) {
      console.log("跳过预加载：网络或设备条件不佳");
      return;
    }

    // 如果没有传入 assets，则使用 import.meta.glob 获取 assets 目录下的图片和视频文件
    const allAssets = assets
      ? assets
      : Object.values(
          import.meta.glob("/src/assets/imgs/**/*.{jpg,png,svg,mp4,webp}")
        );

    // 过滤掉黑名单中的资源
    const assetsToPreload = filterAssetsByBlacklist(allAssets);

    // 分批预加载资源
    batchPreloadAssets(assetsToPreload, batchSize, priority);
  };

  const filterAssetsByBlacklist = (assets: any[], customBlacklist?: string[]): any[] => {
    const activeBlacklist = customBlacklist || blacklist;
    if (activeBlacklist.length === 0) {
      return assets;
    }

    return assets.filter((asset) => {
      let assetPath: string;
      // 处理不同类型的资源路径
      if (typeof asset === "string") {
        assetPath = asset;
      } else if (asset.name) {
        assetPath = asset.name;
      } else if (typeof asset === "function") {
        // 对于 import.meta.glob 返回的函数，从函数名或其他方式获取路径
        assetPath = asset.toString();
      } else {
        assetPath = String(asset);
      }

      // 检查资源路径是否包含黑名单中的任何关键词
      const isBlacklisted = activeBlacklist.some((blacklistItem) =>
        assetPath.toLowerCase().includes(blacklistItem.toLowerCase())
      );

      if (isBlacklisted) {
        return false;
      }

      return true;
    });
  };

  const shouldPreload = (): boolean => {
    // 检查网络连接
    if ("connection" in navigator) {
      const connection = (navigator as any).connection;
      // 如果是慢速网络，跳过预加载
      if (
        connection.effectiveType === "slow-2g" ||
        connection.effectiveType === "2g"
      ) {
        return false;
      }
      // 如果用户开启了数据节省模式，跳过预加载
      if (connection.saveData) {
        return false;
      }
    }

    // 检查设备内存（如果支持）
    if ("deviceMemory" in navigator && (navigator as any).deviceMemory < 4) {
      return false;
    }

    return true;
  };

  const batchPreloadAssets = (
    assetsToPreload: any[],
    stageBatchSize: number = batchSize,
    stagePriority: "high" | "low" = priority,
    stageNumber?: number
  ) => {
    const batches: any[][] = [];
    for (let i = 0; i < assetsToPreload.length; i += stageBatchSize) {
      batches.push(assetsToPreload.slice(i, i + stageBatchSize));
    }

    let currentBatch = 0;
    const processBatch = () => {
      if (currentBatch >= batches.length) {
        if (stageNumber) {
          console.log(`第${stageNumber}阶段预加载完成`);
        }
        return;
      }

      const batch = batches[currentBatch];
      const promises = batch.map((loadAsset: any) =>
        preloadSingleAsset(loadAsset, stagePriority)
      );

      Promise.allSettled(promises).then((results) => {
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        if (stageNumber) {
          console.log(`第${stageNumber}阶段批次${currentBatch + 1}完成: 成功${successful}, 失败${failed}`);
        }
        
        currentBatch++;
        // 使用 requestIdleCallback 或 setTimeout 来处理下一批
        if ("requestIdleCallback" in window) {
          requestIdleCallback(processBatch, { timeout: 5000 });
        } else {
          setTimeout(processBatch, 100);
        }
      });
    };

    // 开始处理第一批
    if ("requestIdleCallback" in window) {
      requestIdleCallback(processBatch, { timeout: 5000 });
    } else {
      setTimeout(processBatch, 100);
    }
  };

  const preloadSingleAsset = async (
    loadAsset: any,
    assetPriority: "high" | "low" = priority
  ): Promise<void> => {
    try {
      let fileUrl: string;

      if (typeof loadAsset === "function") {
        const module = (await loadAsset()) as { default: string };
        fileUrl = module.default;
      } else {
        fileUrl = loadAsset;
      }

      return new Promise((resolve, reject) => {
        if (fileUrl.endsWith(".mp4")) {
          // 视频文件预加载
          const video = document.createElement("video");
          video.preload = "metadata"; // 只预加载元数据，不预加载整个视频
          video.onloadedmetadata = () => resolve();
          video.onerror = () =>
            reject(new Error(`Failed to preload video: ${fileUrl}`));
          video.src = fileUrl;
        } else {
          // 图片文件预加载
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () =>
            reject(new Error(`Failed to preload image: ${fileUrl}`));

          // 设置图片优先级
          if ("fetchPriority" in img) {
            (img as any).fetchPriority = assetPriority;
          }

          img.src = fileUrl;
        }
      });
    } catch (error) {
      console.warn("预加载资源失败:", error);
      throw error;
    }
  };
}
