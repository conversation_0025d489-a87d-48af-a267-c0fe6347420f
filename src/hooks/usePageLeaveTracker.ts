import { useAppStore } from "@/stores/app";
import { storeToRefs } from "pinia";
import { onMounted, onBeforeUnmount, ref } from "vue";

//计算停留时长
export function usePageLeaveTracker(initParams: any) {
  const isEndSendOK = ref(false);
  const appStore = useAppStore();
  const { eventInitData, eventPageInfo } = storeToRefs(appStore);

  function report() {
    if (isEndSendOK.value) return;
    isEndSendOK.value = true;
    if (eventInitData.value?.visit_id) {
      const enname = eventPageInfo.value?.en_name;
      const utm = eventPageInfo.value?.utm;
      const from_page = eventPageInfo.value?.from_page;
      const subpage = eventPageInfo.value?.sub_page;
      const params = `visit_id=${eventInitData.value.visit_id}&request_id=${eventInitData.value.request_id}&utm=${utm}&from_page=${subpage ? from_page + subpage : from_page}&sub_page=${subpage}&en_name=${enname}`;
      navigator.sendBeacon(
        `https://website.gamehaza.com/website/event/report?event=leave&${params}`
      );
    }
  }

  const handleVisibilityChange = () => {
    if (document.visibilityState !== "visible") {
      report();
    } else {
      isEndSendOK.value = false;
    }
  };

  onMounted(() => {
    //事件初始化
    appStore.getEventInit(initParams);

    window.addEventListener("beforeunload", report);
    window.addEventListener("pagehide", report);
    window.addEventListener("unload", report);
    document.addEventListener("visibilitychange", handleVisibilityChange);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("beforeunload", report);
    window.removeEventListener("pagehide", report);
    window.removeEventListener("unload", report);
    document.removeEventListener("visibilitychange", handleVisibilityChange);
  });
}
