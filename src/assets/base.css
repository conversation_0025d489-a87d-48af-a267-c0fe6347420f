* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  overflow-x: hidden;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}
img {
  width: 100%;
  height: 100%;
  display: flex;
}

.font-bold {
  font-weight: bold;
}

input {
  text-indent: 1em;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
  margin: 0;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  &:focus {
    outline: none;
    box-shadow: none;
    border: none;
  }
  &:active {
    outline: none;
    box-shadow: none;
  }
}

input::placeholder {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
}

.bounce {
  animation: bounce 2s infinite;
}

.breathe {
  animation: breathe 2s ease-in-out infinite;
}

/* 统一的 slide-fade 动画 */
.slide-fade-enter-active {
  transition: all 0.6s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.4s ease-in;
}

.slide-fade-enter-from {
  transform: translateY(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}

/* 标题专用的 slide-fade 动画 */
.title-slide-fade-enter-active {
  transition: all 0.8s ease-out;
  transition-delay: 0.2s;
}

.title-slide-fade-leave-active {
  transition: all 0.5s ease-in;
}

.title-slide-fade-enter-from {
  transform: translateY(40px);
  opacity: 0;
}

.title-slide-fade-leave-to {
  transform: translateY(-30px);
  opacity: 0;
}
