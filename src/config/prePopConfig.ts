// 预约弹窗配置接口
export interface OrderPopupConfig {
  id: string; // 弹窗唯一标识
  name: string; // 弹窗名称
  component: string; // 弹窗组件名（如：popupOrderA, popupOrderB, popupOrderC）
  events: {
    onShow: string[]; // 显示时触发的事件
    onClose?: string[]; // 关闭时触发的事件
    onSuccess?: string[]; // 成功时触发的事件
  };
}

// 通用弹窗配置接口
export interface PopupConfig {
  id: string; // 弹窗唯一标识
  name: string; // 弹窗名称
  component: string; // 弹窗组件名
  trigger: {
    condition: 'onLoad' | 'afterOrder' | 'afterStore' | 'manual'; // 触发条件
    delay: number; // 延迟时间（毫秒）
    checkConditions?: string[]; // 检查条件（如：!orderPhone.value）
  };
  closeButton: {
    enabled: boolean; // 是否显示关闭按钮
    showDelay: number; // 关闭按钮显示延迟（毫秒）
  };
  onClose?: {
    nextPopup?: string; // 关闭后的下一个弹窗ID
    delay: number; // 下一个弹窗延迟时间（毫秒）
    condition?: string; // 触发下一个弹窗的条件
  };
  onSuccess?: {
    nextPopup?: string; // 成功后的下一个弹窗ID
    delay: number; // 下一个弹窗延迟时间（毫秒）
  };
  events: {
    onShow: string[]; // 显示时触发的事件
    onClose?: string[]; // 关闭时触发的事件
    onSuccess?: string[]; // 成功时触发的事件
  };
  retention?: {
    enabled: boolean; // 是否启用挽留弹窗
    popupId: string; // 挽留弹窗ID
  };
  priority: number; // 优先级（数字越小优先级越高）
  maxShowCount?: number; // 最大显示次数
  cooldown?: number; // 冷却时间（毫秒）
}

// 弹窗流程配置
export interface PopupFlowConfig {
  flowId: string; // 流程ID
  name: string; // 流程名称
  enabled: boolean; // 是否启用
  orderPopups: OrderPopupConfig[]; // 预约弹窗列表（支持多个随机选择）
  otherPopups: PopupConfig[]; // 其他弹窗列表
  globalSettings: {
    enableRetention: boolean; // 是否启用挽留流程
    storageKey: string; // 本地存储键名
  };
}

// 落地页弹窗流程配置
const landingPagePopupFlow: PopupFlowConfig = {
  flowId: 'landing_page_flow',
  name: '落地页弹窗流程',
  enabled: true,
  globalSettings: {
    enableRetention: true,
    storageKey: 'popup_flow_state',
  },
  // 预约弹窗配置（支持多个等概率随机选择）
  orderPopups: [
    {
      id: 'order_popup_a',
      name: '预约弹窗A',
      component: 'popupOrderA',
      events: {
        onShow: ['imp_popup_all', 'imp_popup1'],
        onClose: ['close_popup_all', 'close_popup1'],
        onSuccess: ['register_popup_all', 'register_popup1'],
      },
    },
    {
      id: 'order_popup_b',
      name: '预约弹窗B',
      component: 'popupOrderB',
      events: {
        onShow: ['imp_popup_all', 'imp_popup2'],
        onClose: ['close_popup_all', 'close_popup2'],
        onSuccess: ['register_popup_all', 'register_popup2'],
      },
    },
    {
      id: 'order_popup_c',
      name: '预约弹窗C',
      component: 'popupOrderC',
      events: {
        onShow: ['imp_popup_all', 'imp_popup3'],
        onClose: ['close_popup_all', 'close_popup3'],
        onSuccess: ['register_popup_all', 'register_popup3'],
      },
    },
  ],
  // 其他弹窗配置
   otherPopups: [
    {
       id: 'retention_popup',
       name: '挽留弹窗',
       component: 'popupRetention',
       trigger: {
         condition: 'manual',
         delay: 0,
       },
       closeButton: {
         enabled: true,
         showDelay: 0, // 立即显示关闭按钮
       },
       onClose: {
         // 关闭挽留弹窗后回到第一个预约弹窗
         delay: 0,
       },
       events: {
         onShow: ['show_retention'],
         onClose: ['cancel_register'],
       },
       priority: 0, // 最高优先级
     },
    {
      id: 'store_popup',
      name: '商店预约弹窗',
      component: 'popupOrderAfterB',
      trigger: {
        condition: 'afterOrder',
        delay: 1000,
      },
      closeButton: {
        enabled: true,
        showDelay: 3000,
      },
      onSuccess: {
        nextPopup: 'community_popup',
        delay: 1000,
      },
      events: {
        onShow: ['show_store_popup'],
        onSuccess: ['click_store'],
      },
      priority: 3,
    },
    {
      id: 'community_popup',
      name: '社区FB预约弹窗',
      component: 'popupDcFb',
      trigger: {
        condition: 'afterStore',
        delay: 1000,
      },
      closeButton: {
        enabled: true,
        showDelay: 3000,
      },
      events: {
        onShow: ['show_community_popup'],
        onSuccess: ['click_community'],
      },
      priority: 4,
    },
  ],
};

// 导出配置
export const prePopConfig = {
  landingPageFlow: landingPagePopupFlow,
  // 可以添加更多流程配置
  // otherFlow: otherPopupFlow,
};

export default prePopConfig;
