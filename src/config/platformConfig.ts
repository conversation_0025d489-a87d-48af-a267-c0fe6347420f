import type { AnalyticsChannel, PlatformMap } from "@/hooks/useAnalytics";

export const utmMap: { [key: string]: AnalyticsChannel } = {
  facebook: "meta",
  tiktok: "tiktok",
  google: "ga4Utm",
  pangle: "tiktok",
  line: "line",
  // google1: "ga4Utm",
  // google2: "ga4Utm",
  // "google2-sa": "ga4Utm",
};

export const platformMap: PlatformMap = {
  pre_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d775bdb6-4130-4332-94f8-d048a900b643",
  },
  pre_google: {
    channel: "ga4Utm",
    eventName: "conversion",
    id: "AW-17261808101",
    params: {
      send_to: "AW-17261808101/0TAKCJKVkeIaEOWTiadA",
    },
    storeClick: {
      ios: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/a-J9CJiVkeIaEOWTiadA",
        },
      },
      gp: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/R3I3CJWVkeIaEOWTiadA",
        },
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=2f82bbf4-8ed5-47eb-b0fc-8f8ceb554387",
  },
  pre_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=0d9ca63e-46db-4c5d-aef0-7dcb7a60af12",
  },
  pre_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d9c1b39a-eea0-47fd-8c7d-7459a1472921",
  },
  pre_line: {
    channel: "line",
    eventName: "Conversion",
    id: "77a2aaaa-9b66-4c49-83df-3b28f37e88f1",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=0ff87ebc-71b7-46ae-8334-8725fb882aae",
    params: ["77a2aaaa-9b66-4c49-83df-3b28f37e88f1"],
  },
  pre2_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d775bdb6-4130-4332-94f8-d048a900b643",
  },
  pre2_google: {
    channel: "ga4Utm",
    eventName: "conversion",
    id: "AW-17261808101",
    params: {
      send_to: "AW-17261808101/0TAKCJKVkeIaEOWTiadA",
    },
    storeClick: {
      ios: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/a-J9CJiVkeIaEOWTiadA",
        },
      },
      gp: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/R3I3CJWVkeIaEOWTiadA",
        },
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=2f82bbf4-8ed5-47eb-b0fc-8f8ceb554387",
  },
  pre2_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=0d9ca63e-46db-4c5d-aef0-7dcb7a60af12",
  },
  pre2_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d9c1b39a-eea0-47fd-8c7d-7459a1472921",
  },
  pre2_line: {
    channel: "line",
    eventName: "Conversion",
    id: "77a2aaaa-9b66-4c49-83df-3b28f37e88f1",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=0ff87ebc-71b7-46ae-8334-8725fb882aae",
    params: ["77a2aaaa-9b66-4c49-83df-3b28f37e88f1"],
  },
  pre3_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=b8b71748-81a3-4786-b385-357d20da78d4",
  },
  pre3_google: {
    channel: "ga4Utm",
    eventName: "conversion",
    id: "AW-17261808101",
    params: {
      send_to: "AW-17261808101/0TAKCJKVkeIaEOWTiadA",
    },
    storeClick: {
      ios: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/a-J9CJiVkeIaEOWTiadA",
        },
      },
      gp: {
        eventName: "conversion",
        params: {
          send_to: "AW-17261808101/R3I3CJWVkeIaEOWTiadA",
        },
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=8e3d16ec-5545-4ff8-8b1e-ff7ac6bb512e",
  },
  pre3_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=cd1c8660-0d34-4da4-a5f8-d4ba0ca0f69b",
  },
  pre3_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=0e108b40-d888-43a0-b24f-d133bed2ce16",
  },
  pre3_line: {
    channel: "line",
    eventName: "Conversion",
    id: "77a2aaaa-9b66-4c49-83df-3b28f37e88f1",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=8c7bbd24-341e-49db-b200-58c74f85e83e",
    params: ["77a2aaaa-9b66-4c49-83df-3b28f37e88f1"],
  },
  shorts1_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=bd422085-b52b-43c3-9ba7-7729e22ea5b7",
  },
  shorts1_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=bd422085-b52b-43c3-9ba7-7729e22ea5b7",
  },
  shorts1_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=bd422085-b52b-43c3-9ba7-7729e22ea5b7",
  },
  shorts2_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d50847a9-30f2-4bf6-a953-b71e48e52c25",
  },
  shorts2_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d50847a9-30f2-4bf6-a953-b71e48e52c25",
  },
  shorts2_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d50847a9-30f2-4bf6-a953-b71e48e52c25",
  },
  shorts3_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=a18500b7-4995-408b-8d58-6d67d34b06cc",
  },
  shorts3_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=a18500b7-4995-408b-8d58-6d67d34b06cc",
  },
  shorts3_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=a18500b7-4995-408b-8d58-6d67d34b06cc",
  },
  shorts4_facebook: {
    channel: "meta",
    eventName: "CompleteRegistration",
    storeClick: {
      ios: {
        eventName: "ios_click_fb",
      },
      gp: {
        eventName: "gp_click_fb",
      },
    },
    id: "511373721997589",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d1ad4f2d-2bc1-443f-8493-afc2bae2926d",
  },
  shorts4_tiktok: {
    channel: "tiktok",
    eventName: "Subscribe",
    id: "D1DLM7RC77U2P4BEM290",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d1ad4f2d-2bc1-443f-8493-afc2bae2926d",
  },
  shorts4_pangle: {
    channel: "tiktok",
    eventName: "Subscribe",
    storeClick: {
      ios: {
        eventName: "ClickButton",
      },
      gp: {
        eventName: "ClickButton",
      },
    },
    id: "D1DLM7RC77U2P4BEM290",
    gp_link: "https://play.google.com/store/apps/details?id=com.yblbtw.gpgat",
    ios_cpp_link:
      "https://apps.apple.com/us/app/%E8%B6%85%E8%9D%A6%E5%A4%A7%E4%BD%9C%E6%88%B0/id6746176096?ppid=d1ad4f2d-2bc1-443f-8493-afc2bae2926d",
  },
  common_ga4: {
    channel: "ga4",
    eventName: "",
    id: "G-D68V8X7B7H",
  },
};

export const getCommonGa4Config = () => {
  let ga4Config = platformMap[`common_ga4`];
  if (!ga4Config) {
    ga4Config = platformMap[`common_ga4`];
  }
  return ga4Config;
};

export const eventConfig = {
  register_all: "register_all",
  register_shorts: "register_shorts",
  click_appstore_all: "click_appstore_all",
  register_page: "register_page",
  click_gp_page: "click_gp_page",
  click_ios_page: "click_ios_page",
  click_gp_float: "click_gp_float",
  click_ios_float: "click_ios_float",
  click_gp_all: "click_gp_all",
  click_gp_shorts: "click_gp_shorts",
  click_ios_all: "click_ios_all",
  click_ios_shorts: "click_ios_shorts",
  game_toss: "game_toss",
  game_signin: "game_signin",
  dc_follow: "dc_follow",
  fb_follow: "fb_follow",
  fb_share: "fb_share",
  register_popup_all: "register_popup_all",
  register_popup1: "register_popup1",
  register_popup2: "register_popup2",
  register_popup3: "register_popup3",
  register_popup4: "register_popup4",
  register_popup5: "register_popup5",
  close_popup_all: "close_popup_all",
  close_popup1: "close_popup1",
  close_popup2: "close_popup2",
  close_popup3: "close_popup3", //新增
  close_popup4: "close_popup4", //7.29新增
  close_popup5: "close_popup5", //8.1新增
  click_gp_afterphone1: "click_gp_afterphone1",
  click_ios_afterphone1: "click_ios_afterphone1",
  imp_popup1: "imp_popup1",
  imp_popup2: "imp_popup2",
  imp_popup3: "imp_popup3", //新增
  imp_popup4: "imp_popup4", //7.28新增
  imp_popup5: "imp_popup5", //8.1新增
  imp_popup_all: "imp_popup_all",
  confirm_register: "confirm_register",
  cancel_register: "cancel_register",
};
