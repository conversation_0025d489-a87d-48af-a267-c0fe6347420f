// 定义顺序数组
export const areaIndexs = ["886", "852", "853", "65", "60"];
export const areaConfig: {
  [key: string]: {
    code: string;
    name: string;
    reg: RegExp;
    placeholder: string;
    maxLength: number;
  };
} = {
  "886": {
    code: "tw",
    name: "台灣",
    reg: /^9\d{8}$/,
    placeholder: "9xxxxxxxx（共9位）",
    maxLength: 9,
  },
  "852": {
    code: "hk",
    name: "香港",
    reg: /^([2|4|5|6|7|8|9|])\d{7}$/,
    placeholder: "xxxxxxxx（共8位）",
    maxLength: 8,
  },
  "853": {
    code: "macao",
    name: "澳門",
    reg: /^6\d{7}$/,
    placeholder: "xxxxxxxx（共8位）",
    maxLength: 8,
  },
  "65": {
    code: "sg",
    name: "新加坡",
    reg: /^[89]\d{7}$/,
    placeholder: "xxxxxxxx（共8位）",
    maxLength: 8,
  },
  "60": {
    code: "my",
    name: "馬來西亞",
    reg: /^1\d{8,9}$/,
    placeholder: "1xxxxxxxx(共9/10位)",
    maxLength: 10,
  },
};

export const packageType = {
  yblb_order_iphone16: "yblb_order_iphone16",
  yblb_order_lemon: "yblb_order_lemon",
  yblb_store_1000: "yblb_store_1000",
  yblb_store_switch2: "yblb_store_switch2",
  yblb_community_avatar: "yblb_community_avatar",
  yblb_community_rayneo: "yblb_community_rayneo",
  yblb_lottery_new1: "yblb_lottery_new1",
  yblb_lottery_new2: "yblb_lottery_new2",
  yblb_lottery_title: "yblb_lottery_title",
  yblb_lottery_lemon: "yblb_lottery_lemon",
  yblb_lottery_dj: "yblb_lottery_dj",
  yblb_lottery_sony: "yblb_lottery_sony",
};

export const lotteryAddType = {
  sign_in: "sign_in",
  fb_share: "fb_share",
};
